tableextension 60007 "Prod. Order Line FLX" extends "Prod. Order Line"
{
    fields
    {
        field(60000; "Lot No. FLX"; Code[50])
        {
            Caption = 'Lot No.';
            ToolTip = 'Specifies the value of the Lot No. field.';
        }
        field(60002; "Piece FLX"; Integer)
        {
            Caption = 'Piece';
            ToolTip = 'Specifies the value of the Piece FLX field.';
        }
        field(60001; "Hose Length FLX"; Decimal)
        {
            Caption = 'Hose Lenght';
            ToolTip = 'Specifies the value of the Hose Length FLX field.';
        }
        field(60003; "Flexati Shipment Date FLX"; Date)
        {
            Caption = 'Flexati Shipment Date';
            ToolTip = 'Specifies the value of the Flexati Shipment Date FLX field.';
        }
        field(60004; "Customer No. FLX"; Code[20])
        {
            Caption = 'Customer No.';
            TableRelation = Customer."No.";
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Customer No. FLX field.';
        }
        field(60005; "Customer Name FLX"; Text[100])
        {
            Caption = 'Customer Name';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Customer Name FLX field.';
        }
        field(60006; "Source Line No. FLX"; Integer)
        {
            Caption = 'Source Line No.';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Source Line No. FLX field.';
        }
        field(60007; "Ship-to Code FLX"; Code[10])
        {
            Caption = 'Ship-to Code';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Ship-to Code FLX field.';
        }
        // field(60008; "Quantity Shipped FLX"; Decimal)
        // {
        //     Caption = 'Quantity Shipped';
        //     AllowInCustomizations = Always;
        // }
        field(60009; "Inventory FLX"; Decimal)
        {
            CalcFormula = sum("Item Ledger Entry".Quantity where("Item No." = field("Item No."),
                                                                  "Variant Code" = field("Variant Code"),
                                                                  "Lot No." = field("Lot No. FLX")));
            Caption = 'Inventory';
            AllowInCustomizations = Always;
            DecimalPlaces = 0 : 5;
            Editable = false;
            FieldClass = FlowField;
            ToolTip = 'Specifies the value of the Inventory FLX field.';
        }
        field(60010; "Blocked FLX"; Boolean)
        {
            AllowInCustomizations = Always;
            Caption = 'Blocked';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Production Order".Blocked where(Status = field(Status), "No." = field("Prod. Order No.")));
        }
        field(60011; "Note FLX"; Text[500])
        {
            AllowInCustomizations = Always;
            Caption = 'Note';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Production Order"."Note FLX" where(Status = field(Status), "No." = field("Prod. Order No.")));
            ToolTip = 'Specifies the value of the Note FLX field.';
        }
        field(60012; "Stop FLX"; Boolean)
        {
            Caption = 'Stop';
            ToolTip = 'Specifies the value of the Stop FLX field.';
        }
        field(60013; "Line Note FLX"; Text[500])
        {
            Caption = 'Line Note';
            ToolTip = 'Specifies the value of the Line Note FLX field.';
        }
        // field(60014; "Source Sales Order No. FLX"; Code[20])
        // {
        //     AllowInCustomizations = Always;
        //     Caption = 'Source Sales Order No.';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Production Order"."Source Sales Order No. FLX" where(Status = field(Status), "No." = field("Prod. Order No.")));
        // }
        // field(60015; "Source Sls. Order Line No. FLX"; Integer)
        // {
        //     AllowInCustomizations = Always;
        //     Editable = false;
        //     Caption = 'Source Sales Order Line No.';
        //     FieldClass = FlowField;
        //     CalcFormula = lookup("Production Order"."Source Sls.Order Line No. FLX" where(Status = field(Status), "No." = field("Prod. Order No.")));
        // }
        // field(60016; "Scrap Quantity FLX"; Decimal)
        // {
        //     AllowInCustomizations = Always;
        //     Caption = 'Scrap Quantity';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Item Ledger Entry".Quantity where("Document No." = field("Prod. Order No."),
        //                                                             "Location Code" = const('ISKARTA'),
        //                                                             "Order Type" = const(Production),
        //                                                             "Entry Type" = const(Output)));
        // }
        field(60017; "Your Reference FLX"; Text[35])
        {
            Caption = 'Your Reference';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Your Reference FLX field.';
        }
        field(60018; "Ship-to Code FlowField FLX"; Code[10])
        {
            Caption = 'Ship-to Code FlowField';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Header"."Ship-to Code" where("No." = field("Prod. Order No.")));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Ship-to Code FlowField FLX field.';
        }
        // field(60019; "Inventory FLX"; Decimal)
        // {
        //     Caption = 'Inventory';
        //     Editable = false;
        //     FieldClass = FlowField;
        //     CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" where("Item No." = field("Item No."), "Lot No." = field("Lot No. FLX")));
        // }
        field(60008; "ID (mm) FLX"; Decimal)
        {
            Caption = 'ID (mm)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."ID (mm) FLX" where("No." = field("Item No.")));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the ID (mm) FLX field.';
        }
        field(60014; "Sales Order Date FLX"; Date)
        {
            Caption = 'Sales Order Date';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Header"."Order Date" where("No." = field("Prod. Order No.")));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Sales Order Date FLX field.';
        }
        field(60015; "Planned Shipment Date FLX"; Date)
        {
            Caption = 'Planned Shipment Date';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Line"."Planned Shipment Date" where("Document No." = field("Prod. Order No."), "Line No." = field("Source Line No. FLX")));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Planned Shipment Date FLX field.';
        }
        field(60016; "Shipped Qty. FLX"; Decimal)
        {
            Caption = 'Shipped Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry".Quantity where("Item No." = field("Item No."), "Entry Type" = const(Sale), "Lot No." = field("Lot No. FLX")));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Shipped Qty. FLX field.';
        }
        field(60039; "Consumed Qty. FLX"; Decimal)
        {
            Caption = 'Consumed Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry".Quantity where("Item No." = field("Item No."), "Entry Type" = const(Consumption), "Lot No." = field("Lot No. FLX")));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Consumed Qty. FLX field.';
        }
        /*field(60019; "Completely Shipped FLX old FLX"; Boolean)
        {
            Caption = 'Completely Shipped';
            ToolTip = 'Specifies the value of the Completely Shipped FLX field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Header"."Completely Shipped" where("No." = field("Prod. Order No.")));
            AllowInCustomizations = Always;
        }
        */
        /*field(60020; "Production BOM No. FLX"; Code[20])
        {
            Caption = 'ID (mm)';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup(Item."Production BOM No." where("No." = field("Item No.")));
            AllowInCustomizations = Always;
        }

        field(60021; "BOM Low Level Code FLX"; Integer)
        {
            Caption = 'BOM Low Level Code';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Production BOM Header"."Low-Level Code" where("No." = field("Production BOM No. FLX")));
            AllowInCustomizations = Always;
        }
        */
        field(60022; "Brand FLX"; Text[250])
        {
            Caption = 'Brand';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Brand field.';
        }
        field(60024; "Brand Description FLX"; Text[250])
        {
            Caption = 'Brand Description';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Brand Description field.';
        }
        field(60025; "Cloth FLX"; Text[250])
        {
            Caption = 'Cloth';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Cloth field.';
        }
        field(60026; "Paste FLX"; Text[250])
        {
            Caption = 'Paste';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Paste field.';
        }
        field(60027; "Wire FLX"; Text[250])
        {
            Caption = 'Wire';
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Wire field.';
        }
        field(60028; "FG Qty. FLX"; Decimal)
        {
            Caption = 'FG Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" where("Location Code" = const('FG'),
                              "Item No." = field("Item No."),
                              "Variant Code" = field("Variant Code"),
                              "Lot No." = field("Lot No. FLX")));
            ToolTip = 'Specifies the value of the FG Qty. field.';
        }
        field(60029; "KALITE Qty. FLX"; Decimal)
        {
            Caption = 'KALITE Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" where("Location Code" = const('KALITE'),
                              "Item No." = field("Item No."),
                              "Variant Code" = field("Variant Code"),
                              "Lot No." = field("Lot No. FLX")));
            ToolTip = 'Specifies the value of the KALITE Qty. field.';
        }
        field(60030; "UHD Qty. FLX"; Decimal)
        {
            Caption = 'UHD Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" where("Location Code" = const('UHD'),
                              "Item No." = field("Item No."),
                              "Variant Code" = field("Variant Code"),
                              "Lot No." = field("Lot No. FLX")));
            ToolTip = 'Specifies the value of the UHD Qty. field.';
        }
        field(60031; "YURUMEYEN Qty. FLX"; Decimal)
        {
            Caption = 'YURUMEYEN Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" where("Location Code" = const('YURUMEYEN'),
                              "Item No." = field("Item No."),
                              "Variant Code" = field("Variant Code"),
                              "Lot No." = field("Lot No. FLX")));
            ToolTip = 'Specifies the value of the YURUMEYEN Qty. field.';
        }
        field(60032; "Qty. to Ship FLX"; Decimal)
        {
            Caption = 'Qty. to Ship';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Line"."Qty. to Ship" where("Document No." = field("Prod. Order No."), "Line No." = field("Source Line No. FLX")));
            AllowInCustomizations = Always;
        }
        field(60033; "Sales Order Qty. FLX"; Decimal)
        {
            Caption = 'Sales Order Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Line"."Quantity (Base)" where("Document No." = field("Prod. Order No."), "Line No." = field("Source Line No. FLX")));
            AllowInCustomizations = Always;
            ToolTip = 'Specifies the value of the Sales Order Qty. field.';
        }
        field(60034; "ISKARTA Qty. FLX"; Decimal)
        {
            Caption = 'ISKARTA Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" where("Location Code" = const('ISKARTA'),
                              "Item No." = field("Item No."),
                              "Variant Code" = field("Variant Code"),
                              "Lot No." = field("Lot No. FLX")));
            ToolTip = 'Specifies the value of the ISKARTA Qty. field.';
        }
        field(60035; "SUPHELI Qty. FLX"; Decimal)
        {
            Caption = 'SUPHELI Qty.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = sum("Item Ledger Entry"."Remaining Quantity" where("Location Code" = const('SUPHELI'),
                              "Item No." = field("Item No."),
                              "Variant Code" = field("Variant Code"),
                              "Lot No." = field("Lot No. FLX")));
            ToolTip = 'Specifies the value of the SUPHELI Qty. field.';
        }
        field(60036; "Sales Unit Price FLX"; Decimal)
        {
            Caption = 'Sales Unit Price';
            ToolTip = 'Specifies the value of the Unit Price field.';
            AllowInCustomizations = Always;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Line"."Unit Price" where("Document No." = field("Prod. Order No."), "Line No." = field("Source Line No. FLX")));
        }
        field(60037; "Sales Unit of Measure Code FLX"; Code[10])
        {
            Caption = 'Sales Unit of Measure Code';
            ToolTip = 'Specifies the value of the Sales Unit of Measure Code field.';
            AllowInCustomizations = Always;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Line"."Unit of Measure Code" where("Document No." = field("Prod. Order No."), "Line No." = field("Source Line No. FLX")));
        }
        field(60038; "Currency Code FLX"; Code[20])
        {
            Caption = 'Currency Code';
            ToolTip = 'Specifies the value of the Currency Code field.';
            AllowInCustomizations = Always;
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Header"."Currency Code" where("No." = field("Prod. Order No.")));
        }
        field(60020; "Prod. Order - Line No. FLX"; Code[50])
        {
            Caption = 'Prod. Order - Line No.';
            ToolTip = 'Specifies the value of the Prod. Order - Line No. field.';
            Editable = false;
        }
        field(60021; "Sales Order - Line No. FLX"; Code[50])
        {
            Caption = 'Sales Order - Line No.';
            ToolTip = 'Specifies the value of the Sales Order - Line No. field.';
            Editable = false;
        }
        field(60023; "Sales Line Description FLX"; Text[100])
        {
            Caption = 'Sales Line Description';
            ToolTip = 'Specifies the value of the Sales Line Description field.';
            Editable = false;
            AllowInCustomizations = Always;
            // FieldClass = FlowField;
            // CalcFormula = lookup("Sales Line".Description where("Document No." = field("Prod. Order No."), "Line No." = field("Source Line No. FLX")));
        }
        /*field(60039; "Completely Shipped New FLX"; Boolean)
        {
            Caption = 'Completely Shipped';
            ToolTip = 'Specifies the value of the Completely Shipped FLX field.';
            Editable = false;
            FieldClass = FlowField;
            CalcFormula = lookup("Sales Header"."Completely Shipped" where("No." = field("Prod. Order No.")));
            AllowInCustomizations = Always;
        }
        */
    }
}